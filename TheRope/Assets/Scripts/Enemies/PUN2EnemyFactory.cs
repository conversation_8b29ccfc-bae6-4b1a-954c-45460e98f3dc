// Copyright Isto Inc.

using ExitGames.Client.Photon;
using Isto.Core.Beings;
using Isto.Core.Networking;
using Photon.Pun;
using Photon.Realtime;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Zenject;

namespace Isto.Core.Photon
{
    /// <summary>
    /// Factory class responsible for creating enemy game objects using POUN2 networking. This is mirrored from
    /// the PUN2PlayerFactory.
    /// One possible future consideration is to make a generic PUN2Factory that can be used for any game object.
    /// </summary>
    public class PUN2EnemyFactory : MonoBehaviour, IEnemyFactory
    {
        [System.Serializable]
        public class EnemyPrefabEntry
        {
            public string EnemyType;
            public BaseEnemyController EnemyPrefab;
        }


        // UNITY HOOKUP

        [SerializeField] private List<EnemyPrefabEntry> _enemyPrefabs = new List<EnemyPrefabEntry>();
        [SerializeField] private PrefabPool _networkPoolingType;
        [SerializeField] private string _enemySpawnTag = "EnemySpawn";


        // OTHER FIELDS

        private INetworkManager _networkManager;
        private DiContainer _container;


        // INJECTION

        [Inject]
        public void Inject(INetworkManager networkManager, DiContainer container)
        {
            _networkManager = networkManager;
            _container = container;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            switch (_networkPoolingType)
            {
                case PrefabPool.Project:
                    PhotonNetwork.PrefabPool = new ProjectAssetPool();
                    break;
                case PrefabPool.Addressables:
                    PhotonNetwork.PrefabPool = new AddressablesPool();
                    break;
                case PrefabPool.None:
                case PrefabPool.Resources:
                default:
                    break;
            }
        }


        // OTHER METHODS

        public GameObject CreateEnemy(string enemyType)
        {
            GameObject enemy = null;
            Vector3 spawnPosition = Vector3.zero;
            Quaternion spawnRotation = Quaternion.identity;

            GameObject spawnPoint = GameObject.FindGameObjectWithTag(_enemySpawnTag);
            if (spawnPoint != null)
            {
                spawnPosition = spawnPoint.transform.position;
                spawnRotation = spawnPoint.transform.rotation;
            }

            enemy = CreateEnemy(enemyType, spawnPosition, spawnRotation);
            return enemy;
        }

        public GameObject CreateEnemy(string enemyType, Vector3 position, Quaternion rotation)
        {
            EnemyPrefabEntry entry = _enemyPrefabs.FirstOrDefault(e => e.EnemyType == enemyType);

            GameObject enemy = null;

            if (!_networkManager.IsMultiplayerAvailable())
            {
                enemy = Instantiate(entry.EnemyPrefab.gameObject, position, rotation);
            }
            else
            {
                object[] customInitData = { enemyType, position.x, position.y, position.z };

                switch (_networkPoolingType)
                {
                    case PrefabPool.None:
                        enemy = CustomPhotonManualSpawn(entry.EnemyPrefab.gameObject, position, rotation, enemyType);
                        break;
                    case PrefabPool.Project:
#if UNITY_EDITOR
                        string path = AssetDatabase.GetAssetPath(entry.EnemyPrefab.gameObject);
                        enemy = PhotonNetwork.Instantiate(path, position, rotation, group: 0, data: customInitData);
#endif
                        break;
                    case PrefabPool.Addressables:
                    case PrefabPool.Resources:
                    default:
                        enemy = PhotonNetwork.Instantiate(entry.EnemyPrefab.name, position, rotation, group: 0, data: customInitData);
                        break;
                }
            }

            _container.InjectGameObject(enemy);

            return enemy;
        }

        // Copied from examples - in case we want to avoid photon's resource based pool without having to
        // build a replacement pool.
        // Warning: UNTESTED!
        public GameObject CustomPhotonManualSpawn(GameObject enemyPrefab, Vector3 position, Quaternion rotation, string enemyType)
        {
            GameObject enemy = Instantiate(enemyPrefab, position, rotation);
            PhotonView photonView = enemy.GetComponent<PhotonView>();

            if (PhotonNetwork.AllocateViewID(photonView))
            {
                object[] data = new object[]
                {
                    enemyType,
                    enemy.transform.position,
                    enemy.transform.rotation,
                    photonView.ViewID
                };

                RaiseEventOptions raiseEventOptions = new RaiseEventOptions
                {
                    Receivers = ReceiverGroup.Others,
                    CachingOption = EventCaching.AddToRoomCache
                };

                SendOptions sendOptions = new SendOptions
                {
                    Reliability = true
                };

                PhotonNetwork.RaiseEvent(PUN2NetworkManager.NetworkEventCodes.CUSTOM_MANUAL_INSTANTIATION, data, raiseEventOptions, sendOptions);
            }
            else
            {
                Debug.LogError("PUN2EnemyFactory: Failed to allocate a ViewId for enemy.");

                Destroy(enemy);
            }

            return enemy;
        }

        // For the code example above you also would need to hook this up to photon network event callbacks
        // or use our custom event manager instead
        public void OnEvent(EventData photonEvent)
        {
            if (photonEvent.Code == PUN2NetworkManager.NetworkEventCodes.CUSTOM_MANUAL_INSTANTIATION)
            {
                object[] data = (object[])photonEvent.CustomData;

                string enemyType = (string)data[0];
                Vector3 position = (Vector3)data[1];
                Quaternion rotation = (Quaternion)data[2];

                // Find the prefab for this enemy type
                EnemyPrefabEntry entry = _enemyPrefabs.FirstOrDefault(e => e.EnemyType == enemyType);
                if (entry != null && entry.EnemyPrefab != null)
                {
                    GameObject enemy = Instantiate(entry.EnemyPrefab.gameObject, position, rotation);
                    PhotonView photonView = enemy.GetComponent<PhotonView>();
                    photonView.ViewID = (int)data[3];
                }
                else
                {
                    Debug.LogError($"PUN2EnemyFactory: Received spawn event for unknown enemy type: {enemyType}");
                }
            }
        }
    }
}